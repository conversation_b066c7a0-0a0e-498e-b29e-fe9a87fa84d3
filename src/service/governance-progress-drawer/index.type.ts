import { AuthorTypeEnum } from '@src/constant';

export interface IDescribeRiskInstancesInMapParams {
  data: {
    MapId: string;
    Limit: number;
    Offset: number;
    Filters: {
      Name: string;
      Values: string[]
    }[];
    TagList?: Array<{
      Key: string;
      Value: string;
    }>;
  }
}

export interface IRiskInstanceItem {
  InstanceId: string;
  HighRiskCount: number;
  MediumRiskCount: number;
  Tag: string;
  ClaimPerson: string;
  Url: string;
  Region: string;
  Product: string;
  NodeId: string;
  TaskId: string;
  IsChange: boolean;
}

export interface IDescribeRiskInstancesInMapResult {
  TotalCount: number;
  RiskInstanceList: IRiskInstanceItem[];
}

export interface ICreateSubscriptionEmailV2Params {
  data: {
    Email: string;
    UserName: string;
    SubUin: string;
  }
}

export interface IUpdateSubscriptionEmailV2Params {
  data: {
    Id: number;
    Email: string;
    UserName: string;
    SubUin: string;
  }
}

export interface IupdateInstanceToClaimedStatusInMapParams {
  data: {
    MapId: string;
    Operate: string;
    MapRiskInstanceList: {
      InstanceId: string;
      Product: string
    } [];
    ClaimPerson: string;
    ClaimUin: string;
  }
}

export interface IUpdateInstanceToIgnoredStatusInMapParams {
  data: {
    MapId: string;
    Operate: string;
    MapRiskInstanceList: {
      InstanceId: string;
      Product: string
    } [];
    Person: string;
    Reason: string;
  }
}

export interface IDescribeRiskManageSubjectListParams {
  data: {
    ArchId: string;
    Limit: number;
    Offset: number;
  }
}

export interface IScanRiskManageSubject {
  /** 主题 ID (int64) */
  Id: number;
  /** 标题 */
  Title: string;
  /** 描述 */
  Description: string;
  /** 来源（1: Agent, 2: 自定义） */
  Source: number;
  /** 创建时间 (格式: YYYY-MM-DD HH:mm:ss) */
  CreateTime: string;
  /** 会话ID (用于查看Agent生成内容) */
  SessionId: string;
  /** 聊天ID (用于查看Agent生成内容) */
  ChatId: string;
  /** 作者 */
  Author: string;
}

export interface IDescribeRiskManageSubjectListResponse {
  Total: number;
  Items: IScanRiskManageSubject[];
}

export interface IFilters {
  /** 过滤名称 */
  Name: string;
  /** 过滤值 */
  Values: string[];
}

export interface ITag {
  /** key */
  Key: string;
  /** value */
  Value: string;
}

export interface IDescribeRiskManageInstanceListParams {
  data: {
    /** 主题 ID */
    SubjectId?: number;
    /** 架构图 ID */
    ArchId?: string;
    /** 每页行数 */
    Limit?: number;
    /** 偏移量 */
    Offset?: number;
    /** 支持按实例ID(InsId) | 实例名称(Name) ｜ 标签(TagList) ｜ 标签键(TagName)筛选 */
    Filters?: IFilters[];
    /** 通过标签过滤实例 */
    TagList?: ITag[];
  }
}

export interface IArchScanRiskInstanceItem {
  // 实例 ID
  InstanceId?: string;
  // 节点 ID
  NodeUuid?: string;
  // 实例标签
  InstanceTags?: string;
  // 控制台访问地址
  InstanceUrl?: string;
  // 风险等级 (3: 高风险, 2: 中风险)
  Level?: 2 | 3;
  // 认领人
  ClaimPerson?: string;
  // 实例所在地区
  Region?: string;
  // 云服务产品类型 (如 CVM)
  Product?: string;
  NodeName?: string;
  TaskId?: string;
}

export interface IDescribeRiskManageInstanceListResponse {
  /** 总数 */
  Total: number;
  /** 实例列表 */
  InstanceItems: IArchScanRiskInstanceItem[];
}

export interface ICreateRiskManageInstanceParams {
  data: {
    /** 主题 ID */
    SubjectId: number;
    /** 实例 ID */
    InstanceId: string;
    /** 架构图ID */
    ArchId: string;
    /** 节点 UUID */
    NodeUuid?: string;
  }
}

export interface IScanRiskManageInstance {
  /** 节点 UUID */
  NodeUuid: string;
  /** 实例 ID */
  InstanceId: string;
}

export interface IUpdateRiskManageSubjectParams {
  data: {
    /** 架构图 ID */
    ArchId: string;
    /** 主题标题 */
    Title: string;
    /** 描述 */
    Description: string;
    /** 治理项 ID,更新时必传 */
    Id?: number;
    /** 来源（1：agent,2:自定义） */
    Source?: number;
    /** 风险实例信息，创建必传 */
    InstanceItems?: IScanRiskManageInstance[];
    /** 会话 ID，创建必传 */
    SessionId?: string;
    /** Agent 会话 ID，创建必传 */
    ChatId?: string;
    /** 创建人名称 */
    Author?: string;
    AuthorType?: AuthorTypeEnum;
  }
}

export interface IUpdateRiskManageSubjectResponse {
  /** 风险主题 ID */
  Id: number;
}

export interface IDeleteRiskManageSubjectParams {
  data: {
    /** 治理项 ID */
    Id?: number;
    /** 架构图 ID */
    ArchId?: string;
    Username?: string;
  }
}

export interface IDescribeInsightMessageParams {
  data: {
    /** 架构图ID */
    ArchId?: string;
    /** 会话ID */
    SessionId?: string;
    /** 对话ID */
    ChatId?: string;
  }
}

export interface IInsightMessage {
  /** 消息内容 */
  Content: string;
  /** 消息类型 */
  ContentType: string;
  /** 创建时间 */
  CreateTime: string;
}

export interface IDescribeInsightMessageResponse {
  /** 消息数组 */
  Messages: IInsightMessage[];
}

export interface IDescribeArchSvgDataParams {
  data: {
    /** 架构图ID */
    ArchId: string;
    /** 版本ID，不传默认为当前版本 */
    VersionId?: number;
    /** 是否3d图，不传默认2d */
    IsThreeDimension?: boolean;
  }
}

export interface IDescribeArchSvgDataResponse {
  /** 架构ID */
  ArchId: string;
  /** svg图片信息 */
  Svg: string;
}

export interface IDescribeArchCSCRiskInfosParams {
  data: {
    MapId: string;
    Limit?: number;
    Offset?: boolean;
    Filters: {
      Name: string;
      Values: string[]
    }[];
    TagList?: Array<{
      Key: string;
      Value: string;
    }>;
  }
}

export interface IDescribeArchCSCRiskInfosResponse {
  CSCRiskInstanceList: {
    InstanceId: string;
    HighRiskCount: number;
    MediumRiskCount: number;
    Tag: string;
    ClaimPerson: string;
    Url: string;
    Region: string;
    Product: string;
    BindNodeId: string;
    BindNodeName: string;
    IsChange: boolean;
    TaskId: string;
  }[];
  TotalCount: number;
}
