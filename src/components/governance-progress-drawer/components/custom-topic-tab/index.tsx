import { app } from '@tea/app';
import React, {
  useState, useEffect, useCallback, useMemo, useRef,
} from 'react';
import { Button, TagSearchBox, StatusTip } from '@tencent/tea-component';
import { omit } from 'lodash';
import { describeGroupAndProductInfosHandle } from '@src/service/inspection-settings-modal/final-request';
import { useGlobalSelector } from '@src/store/global/index';
import {
  useGovernanceProgressDrawerStateSelector,
  changeGovernanceProgressDrawerState,
} from '@src/store/governance-progress-drawer/index';
import { useAppContext } from '@src/context/AppContext';
import { TabEnum } from '@src/components/inspection-drawer';
import { useDispatch } from 'react-redux';
import { t } from '@tea/app/i18n';
import { getRiskManageSubjectList } from '@src/service/governance-progress-drawer/final-request';
import SecondaryDrawer from './components/topic-item-drawer';
import { AttributesNameEnum } from './constants/index';
import Item from './components/collapse-topic-item';
import s from './index.module.scss';

/**
 * 自定主题组件
 * @returns React.ReactElement
 */
export default function CustomTopicTab(): React.ReactElement {
  const { pluginPropsData, inspectionDrawerWidth } = useGlobalSelector();
  const dispatch = useDispatch();
  const { chatBiCallBacks } = useAppContext();
  const { customTopicSearchValue, progressTab, customTopicTabHash } = useGovernanceProgressDrawerStateSelector();
  const [data, setData] = useState([]);
  // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
  const [total, setTotal] = useState(0);
  const [totalWithoutFilter, setTotalWithoutFilter] = useState(0);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [defaultId, setDefaultId] = useState<number | undefined>(undefined);
  const [defaultTopicTitle, setDefaultTopicTitle] = useState('');
  const [supportTaskProductList, setSupportTaskProductList] = useState([]);
  // 维护编辑状态和未保存的数据
  const [editingItems, setEditingItems] = useState(new Map());
  const [query, setQuery] = useState<any>({
    ArchId: pluginPropsData.archInfo.archId,
    Limit: 10,
    Offset: 0,
  });
  const [containerWidth, setContainerWidth] = useState(600);
  const containerRef = useRef(null);
  const attributes = useMemo(
    () => [
      {
        type: ['multiple', { all: true }],
        key: AttributesNameEnum.PRODUCTS,
        name: '云产品',
        values: supportTaskProductList,
      },
      {
        type: 'input',
        key: AttributesNameEnum.TOPICS,
        name: '主题',
      },
    ],
    [supportTaskProductList],
  );
  const openDrawer = (id: number, title: string) => {
    setDefaultId(id);
    setDefaultTopicTitle(title);
    setVisible(true);
  };
  const fetchRiskManageSubjectList = useCallback(async (isLoadMore = false) => {
    if (isLoadMore) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      const res = await getRiskManageSubjectList({
        env: pluginPropsData.env,
        apiParams: {
          data: omit(query, 'hash') as any,
        },
        uin: pluginPropsData.uin,
      });
      if (res) {
        const serverData = res?.Items ?? [];
        setData((prevData) => {
          // 保留未保存的数据（没有ID的项目）
          const unsavedItems = prevData.filter(
            (item) => !item.Id && item.tempId,
          );

          if (isLoadMore) {
            // 加载更多时，追加新数据到已有的服务器数据后面
            const existingServerData = prevData.filter((item) => item.Id);
            const mergedData = [...existingServerData, ...serverData, ...unsavedItems];
            return mergedData;
          }
          // 首次加载或刷新时，替换服务器数据
          const mergedData = [...serverData, ...unsavedItems];
          return mergedData;
        });

        setTotal(res?.Total ?? 0);
      }

      if (isLoadMore) {
        setLoadingMore(false);
      } else {
        setLoading(false);
      }
    } catch (error) {
      if (isLoadMore) {
        setLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  }, [pluginPropsData, query]);

  // 获取无条件查询的总数
  const fetchTotalWithoutFilter = useCallback(async () => {
    try {
      const res = await getRiskManageSubjectList({
        env: pluginPropsData.env,
        apiParams: {
          data: {
            ArchId: pluginPropsData.archInfo.archId,
            Limit: 9999, // 只需要获取总数，不需要实际数据
            Offset: 0,
            // 不传递 Filters，获取所有数据的总数
          },
        },
        uin: pluginPropsData.uin,
      });
      if (res) {
        setTotalWithoutFilter(res?.Total ?? 0);
      }
    } catch (error) {
      console.error('获取总数失败:', error);
    }
  }, [pluginPropsData]);

  const onAdd = useCallback(() => {
    // 检查当前主题数量是否已达到上限（使用无条件查询的总数）
    if (totalWithoutFilter >= 10) {
      app.tips.error('当前最多支持10个主题，请删除后再试');
      return;
    }

    const newItem = {
      defaultEditing: true,
      tempId: `temp_${Date.now()}`, // 为新添加的项目生成临时ID
    };
    setData((last) => [...last, newItem]);
    // 将新项目标记为编辑状态
    setEditingItems((prev) => new Map(prev).set(newItem.tempId, {
      isEditing: true,
      editTitle: '',
      editDescription: '',
    }));
  }, [totalWithoutFilter]);

  const onDelete = useCallback(
    (i) => {
      setData((last) => {
        const itemToDelete = last[i];
        const itemKey = itemToDelete.Id || itemToDelete.tempId;

        // 从编辑状态中移除
        setEditingItems((prev) => {
          const newMap = new Map(prev);
          newMap.delete(itemKey);
          return newMap;
        });

        const newData = [...last];
        newData.splice(i, 1);
        return newData;
      });

      // 删除后刷新列表数据
      fetchRiskManageSubjectList(false);
      // 更新无条件查询的总数
      fetchTotalWithoutFilter();
    },
    [fetchRiskManageSubjectList],
  );

  // 加载更多函数
  const loadMore = useCallback(async () => {
    const currentServerDataCount = data.filter((item) => item.Id).length;
    const newOffset = currentServerDataCount;

    setLoadingMore(true);

    try {
      const res = await getRiskManageSubjectList({
        env: pluginPropsData.env,
        apiParams: {
          data: {
            ...omit(query, 'hash'),
            Offset: newOffset, // 直接使用新的偏移量，不更新 query 状态
          } as any,
        },
        uin: pluginPropsData.uin,
      });

      if (res) {
        const serverData = res?.Items ?? [];
        setData((prevData) => {
          // 保留未保存的数据（没有ID的项目）
          const unsavedItems = prevData.filter((item) => !item.Id && item.tempId);
          // 获取现有的服务器数据
          const existingServerData = prevData.filter((item) => item.Id);
          // 追加新数据，避免重复
          const existingIds = new Set(existingServerData.map((item) => item.Id));
          const newServerData = serverData.filter((item) => !existingIds.has(item.Id));
          const mergedData = [...existingServerData, ...newServerData, ...unsavedItems];
          return mergedData;
        });

        setTotal(res?.Total ?? 0);
      }

      setLoadingMore(false);
    } catch (error) {
      setLoadingMore(false);
    }
  }, [data, pluginPropsData, query]);

  // 获取产品和维度信息
  const getProductsGroupsInfo = async () => {
    try {
      const res = await describeGroupAndProductInfosHandle({
        env: pluginPropsData.env,
        uin: pluginPropsData.uin,
      });
      if (!res) {
        return;
      }

      // 处理产品数据
      const productTmp = [];
      const productDict = {};
      res.Products?.forEach((item) => {
        productDict[item.Product] = item.Name;
        productTmp.push({ key: item.Product, name: item.Name });
      });
      setSupportTaskProductList(productTmp);
    } catch (err) {
      const msg = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{msg}}', { msg }));
    }
  };

  const onChange = (v) => {
    const processValue = (last, newValue) => {
      if (JSON.stringify(last) === JSON.stringify(newValue)) {
        setQuery((lastQ) => ({
          ...lastQ,
          hash: `${new Date().getTime()}`,
          Offset: 0,
        }));
      }
      const directInputItem = newValue.find((k: any) => !k?.attr);
      // 删除场景
      if (newValue.length < last.length) {
        return newValue;
      }
      // 已存在关键字场景
      if (
        newValue.filter(
          (k: any) => k?.attr?.key === AttributesNameEnum.TOPICS || !k?.attr,
        ).length > 1
      ) {
        return last;
      }
      // 直输关键字场景
      if (directInputItem && typeof directInputItem === 'object') {
        (directInputItem as any).attr = {
          type: 'input',
          key: AttributesNameEnum.TOPICS,
          name: '主题',
        };
        return newValue;
      }
      return newValue;
    };

    const newValue = processValue(customTopicSearchValue || [], v);
    dispatch(
      changeGovernanceProgressDrawerState({
        customTopicSearchValue: newValue,
      }),
    );
  };

  // 处理编辑状态变化
  const handleEditingChange = useCallback(
    (itemKey: string | number, editingState: any) => {
      setEditingItems((prev) => {
        const newMap = new Map(prev);
        if (editingState) {
          newMap.set(itemKey, editingState);
        } else {
          newMap.delete(itemKey);
        }
        return newMap;
      });
    },
    [],
  );

  // 处理保存成功的回调
  const handleSaved = useCallback(
    (
      savedTempId?: string,
      updatedData?: { id: number; title: string; description: string },
    ) => {
      // 如果是临时项目保存成功，立即从列表和编辑状态中移除
      if (savedTempId) {
        setData((prevData) => prevData.filter((item) => item.tempId !== savedTempId));
        setEditingItems((prev) => {
          const newMap = new Map(prev);
          newMap.delete(savedTempId);
          return newMap;
        });
      }

      // 如果是编辑已存在项目，立即更新本地数据
      if (updatedData) {
        setData((prevData) => prevData.map((item) => (item.Id === updatedData.id
          ? {
            ...item,
            Title: updatedData.title,
            Description: updatedData.description,
          }
          : item)));
      }

      // 然后刷新列表获取最新数据
      fetchRiskManageSubjectList(false);
      // 更新无条件查询的总数
      fetchTotalWithoutFilter();
    },
    [fetchRiskManageSubjectList],
  );

  const getContainerWidth = () => {
    const { clientWidth } = containerRef.current;
    setContainerWidth(clientWidth ?? 600);
  };

  const onAgentAdd = () => {
    dispatch(changeGovernanceProgressDrawerState({
      progressTab: TabEnum.right,
    }));
    console.log(222, chatBiCallBacks);

    chatBiCallBacks?.sendMessage('我想增加一个风险主题');
  };

  useEffect(() => {
    getProductsGroupsInfo();
    fetchTotalWithoutFilter();
  }, []);

  useEffect(() => {
    const filters = [];
    (customTopicSearchValue || []).forEach((v) => {
      if (v?.attr?.key === AttributesNameEnum.TOPICS) {
        filters.push({
          Name: 'Title',
          Values: v.values.map((item) => item.name),
        });
      }
      if (v?.attr?.key === AttributesNameEnum.PRODUCTS) {
        filters.push({
          Name: 'Product',
          Values: v.values.map((item) => item.key),
        });
      }
    });

    // 当filters变化时，清空当前服务器数据，保留未保存的数据
    setData((prevData) => {
      const unsavedItems = prevData.filter((item) => !item.Id && item.tempId);
      return unsavedItems;
    });

    setQuery((last) => ({
      ...last,
      Filters: filters,
      Offset: 0, // 重置偏移量
      ...(progressTab === TabEnum.left || customTopicTabHash ? { hash: `${new Date().getTime()}` } : {}),
    }));
  }, [customTopicSearchValue, progressTab, customTopicTabHash]);

  useEffect(() => {
    fetchRiskManageSubjectList(false);
  }, [JSON.stringify(query)]);

  useEffect(() => {
    getContainerWidth();
  }, [inspectionDrawerWidth]);
  return (
    <div className={s['custom-topic-tab-container']}>
      {visible ? (
        <SecondaryDrawer
          defaultId={defaultId}
          defaultTopicTitle={defaultTopicTitle}
          visible={visible}
          onClose={() => setVisible(false)}
          pluginPropsData={pluginPropsData}
        />
      ) : null}
      <div className={s['custom-topic-tab-btns']}>
        <TagSearchBox
          attributes={attributes as any}
          minWidth="100%"
          value={customTopicSearchValue || []}
          onChange={onChange}
          hideHelp
          onSearchButtonClick={() => fetchRiskManageSubjectList(false) as any}
        />
        <Button
          type="weak"
          disabled={loading || loadingMore}
          className={s['custom-topic-tab-agent-btn']}
          onClick={onAgentAdd}
        >
          Agent添加
        </Button>
        <Button
          disabled={loading || loadingMore}
          type="primary"
          onClick={onAdd}
          className={s['custom-topic-tab-create-btn']}
        >
          手动添加
        </Button>
      </div>
      {loading && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            marginTop: 30,
          }}
        >
          <StatusTip
            // @ts-ignore
            status="loading"
          />
        </div>
      )}
      {!loading && !data.length && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignContent: 'center',
            marginTop: 30,
          }}
        >
          <StatusTip
            // @ts-ignore
            status="empty"
          />
        </div>
      )}

      <div
        ref={containerRef}
        className={s.container}
      >
        <div
          style={{ width: containerWidth + 40 }}
          className={s['container-inner']}
        >
          {data.map((v, i) => {
            const itemKey = v.Id || v.tempId;
            const editingState = editingItems.get(itemKey);

            return (
              <Item
                style={{ width: containerWidth - 4 }}
                // eslint-disable-next-line react/no-array-index-key
                key={`item-${itemKey}-${i}`}
                id={v.Id}
                tempId={v.tempId}
                index={i}
                defaultEditing={v?.defaultEditing}
                onDelete={onDelete}
                onOpenDrawer={openDrawer}
                pluginPropsData={pluginPropsData}
                title={v?.Title}
                description={v?.Description}
                version={v?.ArchVersionId}
                source={v?.Source}
                onSaved={handleSaved}
                editingState={editingState}
                onEditingChange={handleEditingChange}
              />
            );
          })}
          {/* 加载更多按钮 */}
          {!loading && data.filter((item) => item.Id).length < total && (
            <div style={{ textAlign: 'center', marginTop: 20, marginBottom: 20 }}>
              <Button
                type="link"
                loading={loadingMore}
                // eslint-disable-next-line @typescript-eslint/no-misused-promises
                onClick={loadMore}
                disabled={loadingMore}
              >
                {loadingMore ? '加载中...' : '加载更多'}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
