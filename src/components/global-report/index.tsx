/* eslint-disable react/no-array-index-key */
/* eslint-disable max-len */
/* eslint-disable  */
import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  Modal,
  Card,
  ExternalLink,
  Table,
  message,
  Button,
  Alert,
  Icon,
  notification,
} from '@tencent/tea-component';
import moment from 'moment';
import { store } from '@src/store';
const { dispatch } = store;
import { useGlobalSelector } from '@src/store/global/index';
import TvisionTcharts from 'tvision-charts-react';
import reportStop from '@src/assets/svg/report-stop.svg';
import { t, Slot } from '@tea/app/i18n';
import { changeReportData, useReportSelector } from '@src/store/report/index';
import { StatusTip } from '@tencent/tea-component/lib/tips';
import {
  describeArchScanOverviewInfo as DescribeArchScanOverviewInfo,
  describeArchRiskTrendInfo as DescribeArchRiskTrendInfo,
  describeArchStrategyList as DescribeArchStrategyList,
  updateArchScanReportArchiveInfo as UpdateArchScanReportArchiveInfo,
  stopArchScan as StopArchScan,
  fetchArchScanReportTaskStatus,
  describeDownloadTask as downloadTask
  // describeArchitectureNodeRiskInfoV3,
} from '@src/service/export-service/final-request';
import { cloneDeep, max, omit  } from 'lodash';
import { app } from '@tea/app';
import s from './index.module.scss';
import './index.less';
import { clickReport, toUTCtime } from '@src/utils';
import { reportVersion, Env } from '@src/constant';
import CircularProgress from '@src/components/circular-progress';

const isInit = true;
const getProgressNum = 0;
const onlySeeHighRiskVal = false;
const nodeDataOriginList = [];
const record = [];
const isLoading = true;
const isCreate = false;
const currentTaskId = '';
const currentRiskId = 0;
const strategyId = 0;
// let initScanReportTime = null;
let initProgressTime = '';
let progressTimeTimer = null;
let progressBubbleVisibleRe = true;
let isStopScan = false;
const isResetScan = false;
let downloadTimer = null;
let scanReportTimeTimer = null;
(window as any).initScanReportTime = null;
const isLeave = false;

  const resetScanReportInfo = () => {
    scanReportTimeTimer && clearInterval(scanReportTimeTimer);
    scanReportTimeTimer = null;
    // 清除下载任务轮询定时器
    if (downloadTimer) {
      clearTimeout(downloadTimer);
      downloadTimer = null;
    }
    dispatch(changeReportData({
      scanReportTime: null,
    }));
    // initScanReportTime = null;
    (window as any).initScanReportTime = null;
  };

export interface Props {
  archId?: string;
  visible?: boolean;
  resultId?: string;
  downloadInfo?: any;
  onClose?: () => void;
  createArchScanReportFile?: () => Promise<any>;
  stopReportGeneration?: () => void;
  pluginAPI: AppPluginAPI.PluginAPI;
  reportBtnConfigContentRef?: any;
}

export interface ScanReportContentProps {
  bubbleVisible?: boolean;
  changeVisible?: any;
  stopReportGeneration?: () => void;
  progressNum?: number | null;
  stopReportScan?: () => void;
}

const ScanReportContent = ({
  bubbleVisible,
  changeVisible,
  stopReportGeneration,
  progressNum,
  stopReportScan
}: ScanReportContentProps) => {
  const [scanReportTime, setScanReportTime] = useState((window as any).initScanReportTime);
  const scanTimeArr = scanReportTime?.split(':');
  useMemo(() => {
    if (scanReportTimeTimer !== null) {
      clearInterval(scanReportTimeTimer);
      scanReportTimeTimer = null;
    }
    scanReportTimeTimer = setInterval(() => {
      (window as any).initScanReportTime && ((window as any).initScanReportTime = moment((window as any).initScanReportTime, 'HH:mm:ss')
        .add(1, 'seconds')
        .format('HH:mm:ss'));
      if (setScanReportTime) {
        setScanReportTime((window as any).initScanReportTime);
      }
    }, 1000);
  }, [scanReportTime]);
  return (
    <>
      {!bubbleVisible ? (
        <div className="report-scan-wrap">
          <CircularProgress value={progressNum || 0} />
          <span>{t("生成中")}</span>
          <span>
            {scanTimeArr?.length > 2 && parseInt(scanTimeArr?.[0], 10) > 0
              ? scanReportTime
              : `${scanTimeArr?.[1] ?? "00"}:${scanTimeArr?.[2] ?? "00"}`}
          </span>
        </div>
      ) : (
        <div className="report-scan-wrap">
          <CircularProgress value={progressNum || 0} />
          <span>{t("报告文件生成中")}</span>
          <span>
            {scanTimeArr?.length > 2 && parseInt(scanTimeArr?.[0], 10) > 0
              ? scanReportTime
              : `${scanTimeArr?.[1] ?? "00"}:${scanTimeArr?.[2] ?? "00"}`}
          </span>
          <img
            className={s['report-stop']}
            onClick={() => {
              // 报告生成状态
              dispatch(changeReportData({
                scanFail: true
              }));
              // 清空定时器
              resetScanReportInfo();
              stopReportScan();
              // 停止报告生成任务
              stopReportGeneration?.();
              // 关闭气泡
              changeVisible?.();
            }}
            src={reportStop}
          />
        </div>
      )}
    </>
  );
};

const ProgressContent = ({
  bubbleVisible,
  changeVisible,
}: ScanReportContentProps) => {
  const [progressTime, setProgressTime] = useState(initProgressTime);
  const scanTimeArr = progressTime?.split(':');
  useMemo(() => {
    if (progressTimeTimer !== null) {
      clearInterval(progressTimeTimer);
      progressTimeTimer = null;
    }
    progressTimeTimer = setInterval(() => {
      initProgressTime = moment(initProgressTime, 'HH:mm:ss')
        .add(1, 'seconds')
        .format('HH:mm:ss');
      if (setProgressTime) {
        setProgressTime(initProgressTime);
      }
    }, 1000);
  }, [progressTime]);
  return (
    <>
      {!bubbleVisible ? (
        <div className="report-scan-wrap">
          <Icon type="loading" />
          <span>
            {t("架构图绑定实例数超100个，将耗时稍长，可先访问其他应用")}
          </span>
          <span>
            {scanTimeArr?.length > 2 && parseInt(scanTimeArr?.[0], 10) > 0
              ? progressTime
              : `${scanTimeArr?.[1]}:${scanTimeArr?.[2]}`}
          </span>
        </div>
      ) : (
        <div className="report-scan-wrap">
          <Icon type="loading" />
          <span>
            {t("架构图绑定实例数超100个，将耗时稍长，可先访问其他应用")}
          </span>
          <span>
            {scanTimeArr?.length > 2 && parseInt(scanTimeArr?.[0], 10) > 0
              ? progressTime
              : `${scanTimeArr?.[1]}:${scanTimeArr?.[2]}`}
          </span>
          <Icon
            className={s['report-close']}
            onClick={() => {
              changeVisible?.();
            }}
            type="close"
          />
        </div>
      )}
    </>
  );
};

// eslint-disable-next-line no-empty-pattern
const Report = React.forwardRef(({
  createArchScanReportFile,
  stopReportGeneration,
  pluginAPI,
  reportBtnConfigContentRef,
}: Props, ref: any) => {
  // 弹框全局ref
  const visibleRef = useRef(false);
  const { pluginPropsData, inspectionTaskData } = useGlobalSelector();
  const { FinishTime: finishTime } = inspectionTaskData;
  const {
    visible,
    resultId,
    downloadInfo,
    scanFail,
    scanReportBubbleVisible,
    scanReportTime,
    isFinished,
    generating,
    todayInspected,
    progressBubbleVisible,
    progressNum,
    // isInspecting,
    taskTimer,
  } = useReportSelector();
  const { archInfo } = pluginPropsData;
  const { archId } = archInfo;
  const { scrollable } = Table.addons;
  const currentMapId = archId;
  const [records, setRecords] = useState([]);
  const [overviewInfo, setOverviewInfo] = useState<any>({});
  const [riskTrendInfo, setRiskTrendInfo] = useState<any>({});
  const [url, setUrl] = useState('');
  const [riskProList, setRiskProList] = useState([]);
  const [archived, setArchived] = useState(false);
  progressBubbleVisibleRe = progressBubbleVisible;

  const columns = [
    {
      key: 'ProductName',
      header: t('云产品'),
      render: (item) => <div>{item.ProductName}</div>,
    },
    {
      key: 'GroupName',
      header: t('类别'),
      width: 50,
    },
    {
      key: 'StrategyName',
      header: t('评估项名称'),
      render: (item) => <div>{item.StrategyName}</div>,
    },
    {
      key: 'Level',
      header: t('风险等级'),
      width: 70,
    },
    {
      key: 'Condition',
      header: t('告警条件'),
      render: (item) => (
        <div title={item?.Condition?.join('\n')} className="repair-line-wrap">
          {item?.Condition?.map((item, index) => (
            <div key={`condition-${index}`}>{item}</div>
          ))}
        </div>
      ),
    },
    {
      key: 'Notice',
      header: t('评估结果'),
      render: (item) => <div>{item.Notice}</div>,
    },
    // {
    //   key: 'Repair',
    //   header: t('优化建议'),
    //   render: item => {
    //     return <div title={item.Repair} className={'repair-line-wrap'}>
    //       {
    //         item.Repair
    //       }
    //     </div>;
    //   }
    // },
  ];

  const reportBtnConfig: {visible?: boolean; destroyOnClose?: boolean; content?: any} = useMemo(() => {
    let reportBtnConfig = {};
    // if (generating && scanReportTime !== null) {
    if (generating) {
      if (!scanReportBubbleVisible) {
        reportBtnConfig = {
          content: (
            <ScanReportContent
              bubbleVisible={scanReportBubbleVisible}
              stopReportGeneration={stopReportGeneration}
              progressNum={progressNum}
              stopReportScan={()=>{
                visibleRef.current = false
              }}
            />
          ),
        };
      } else {
        reportBtnConfig = {
          visible: true,
          content: (
            <ScanReportContent
              bubbleVisible={scanReportBubbleVisible}
              stopReportGeneration={stopReportGeneration}
              progressNum={progressNum}
              stopReportScan={()=>{
                visibleRef.current = false
              }}
              changeVisible={() => {
                dispatch(changeReportData({ scanReportBubbleVisible: false }));
              }}
            />
          ),
        };
      }
    } else if (
      Object.keys(downloadInfo)?.length > 0
      && scanReportBubbleVisible
    ) {
      if (scanFail) {
        reportBtnConfig = {
          destroyOnClose: true,
          visible: true,
          content: (
            <div className="report-scan-wrap">
              <Icon type="warning" color="#ff7800" />
              <span>{t("生成失败")}</span>
              <Icon
                className={s['report-close']}
                onClick={() => {
                  dispatch(changeReportData({ scanReportBubbleVisible: false }));
                }}
                type="close"
              />
            </div>
          ),
        };
      } else {
        reportBtnConfig = {
          destroyOnClose: true,
          visible: true,
          content: (
            <div className="report-scan-wrap">
              <Icon type="success" color="#0cbf5b" />
              <span>{t("生成成功")}</span>
            </div>
          ),
        };
      }
    }
    return reportBtnConfig;
  }, [
    downloadInfo,
    scanReportTime,
    scanReportBubbleVisible,
    finishTime,
    scanFail,
    progressNum,
  ]);
  useEffect(() => {
    dispatch(changeReportData({
      reportBtnConfig: omit(reportBtnConfig, ['content']),
    }))
    reportBtnConfigContentRef.current = reportBtnConfig?.content;
  }, [reportBtnConfig]);

  useEffect(() => {  
    if(resultId) {
      // 开启轮询进度
      describeDownloadTask(resultId)
    } 
  },[resultId])

  const getPeogressConfig = () => (progressBubbleVisibleRe ? {
    overlayClassName: 'progress-overlay',
    visible: true,
    content: <ProgressContent
      bubbleVisible={progressBubbleVisibleRe}
      changeVisible={
        () => {
          progressBubbleVisibleRe = false;
          dispatch(changeReportData({ progressBubbleVisible: false }));
        }
      }
    />,
  } : {
    overlayClassName: 'progress-overlay',
    content: <ProgressContent bubbleVisible={progressBubbleVisibleRe} />,
  });

  const resetScanInfo = () => {
    // initProgressTime = '';
    clearInterval(progressTimeTimer);
    progressTimeTimer = null;
    progressBubbleVisibleRe = true;
    dispatch(changeReportData({
      progressNum: null,
      progressBubbleVisible: true,
    }));
  };
  const getDescribeArchitectureNodeRiskInfoV3 = async (currentMapId, isNotRender?: boolean, isFinish?: boolean) => {
    // try {
    //   const res = await describeArchitectureNodeRiskInfoV3({
    //     uin: pluginPropsData.uin,
    //     data: {
    //       MapId: currentMapId,
    //     },
    //     env: pluginPropsData.env as Env,
    //   });
    //   if (!res) {
    //     return;
    //   }
    //   setNodeDataOrigin(res.NodeRiskCountList);
    //   if (!isFinish) {
    //     res.NodeRiskCountList.forEach((item) => {
    //       nodeDataOriginList.forEach((el) => {
    //         if (item.NodeUuid === el.NodeUuid && el.inspecting !== undefined) {
    //           item.inspecting = el.inspecting;
    //         }
    //       });
    //     });
    //   }
    //   nodeDataOriginList = res.NodeRiskCountList;
    //   if (!isNotRender) {
    //     renderSubscript(nodeDataOriginList, onlySeeHighRiskVal);
    //   }
    // } catch (err) {
    //   const message = err.msg || err.toString() || t('未知错误');
    //   app.tips.error(t('{{message}}', { message }));
    // }
  };
  const stopScan = () => {
    pluginAPI.setAsyncTaskStop();
    resetScanInfo();
    clearInterval(taskTimer);
    dispatch(changeReportData({
      inspecting: false,
      taskTimer: null,
    }));
    isStopScan = true;
    getDescribeArchitectureNodeRiskInfoV3(currentMapId);
  };
  const stopArchScan = async () => {
    try {
      const res = await StopArchScan({
        uin: pluginPropsData.uin,
        data: {
          ArchId: currentMapId,
        },
        env: pluginPropsData.env as Env,
      });
      if (res.Error) {
        const message = res.Error.Message || '';
        app.tips.error(message);
        return;
      }
    } catch (err) {
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  };
  const getDescribeArchScanOverviewInfo = async () => {
    try {
      const res = await DescribeArchScanOverviewInfo({
        uin: pluginPropsData.uin,
        data: {
          ArchId: currentMapId,
          ResultId: resultId,
        },
        env: pluginPropsData.env as Env,
      });
      setOverviewInfo(res);
      setArchived(res.ReportArchiveStatus);
    } catch (err) {
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  };
  const getDescribeArchRiskTrendInfo = async () => {
    try {
      const res = await DescribeArchRiskTrendInfo({
        uin: pluginPropsData.uin,
        data: {
          ArchId: currentMapId,
          ResultId: resultId,
        },
        env: pluginPropsData.env as Env,
      });
      setRiskTrendInfo(res);
    } catch (err) {
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  };
  const getDescribeArchStrategyList = async () => {
    try {
      const res = await DescribeArchStrategyList({
        uin: pluginPropsData.uin,
        data: {
          ArchId: currentMapId,
          ResultId: resultId,
        },
        env: pluginPropsData.env as Env,
      });
      setRecords(res.ArchStrategyList || []);
      setUrl(res.CosUrl);
    } catch (err) {
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  };
  // eslint-disable-next-line consistent-return
  const updateArchScanReportArchiveInfo = async () => {
    try {
      const res = await UpdateArchScanReportArchiveInfo({
        uin: pluginPropsData.uin,
        data: {
          ArchIds: [currentMapId],
          ResultIds: [resultId],
          ReportVersion: reportVersion,
          SdkName: 'inspect-report-sdk',
        },
        env: pluginPropsData.env as Env,
      });
      if (res.Error) {
        return app.tips.error(t('操作失败'));
      }
      message.success({
        content: t('操作成功'),
      });
      setArchived(true);
    } catch (err) {
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  };
  useMemo(() => {
    visibleRef.current = visible;
    if (visible) {
      getDescribeArchScanOverviewInfo();
      getDescribeArchRiskTrendInfo();
      getDescribeArchStrategyList();
    } else {
      resetScanReportInfo();
      setOverviewInfo({});
      setRiskTrendInfo({});
      setRiskProList([]);
    }
  }, [visible]);
  useMemo(() => {
    if (
      riskTrendInfo.ArchRiskChartInfos?.[1]?.ChartInfoSet?.[0]?.ChartDataInfoSet
        ?.length > 0
    ) {
      const highList = [];
      const midList = [];
      const lowList = [];
      riskTrendInfo.ArchRiskChartInfos?.[1]?.ChartInfoSet?.[0]?.ChartDataInfoSet?.forEach(
        (item) => {
          highList.push(item.KeyValueSet?.[0]?.Value);
          midList.push(item.KeyValueSet?.[1]?.Value);
          lowList.push(item.KeyValueSet?.[2]?.Value);
        },
      );
      setRiskProList(cloneDeep([highList, midList, lowList]));
    }
  }, [riskTrendInfo]);
  useMemo(() => {
    if (isFinished && (window as any).initScanReportTime === null) {
      dispatch(changeReportData({
        scanReportBubbleVisible: false,
      }));
      if (pluginPropsData.env === 'ISA') {
        setTimeout(() => {
          (document as any).querySelector('.inpected-lay-wrap  .tea-notification__close .tea-btn')?.click();
          setTimeout(() => {
            notification.success({
              className: 'inpected-lay-wrap',
              title: t('巡检完成'),
              description: <div>

                已完成对业务架构的巡检，您可以点击节点图标查看风险详情，或者
                <Button
                  style={
                    {
                      position: 'relative',
                      top: '-1px',
                    }
                  }
                  disabled={generating}
                  type="link"
                  onClick={
                    () => {
                      createArchScanReportFile();
                    }
                  }
                >
                  <Slot content={
                    t('查看报告')
                  }
                  />
                </Button>
              </div>,
              duration: 0,
            });
          }, 500);
        }, 10);
      } else {
        (document as any).querySelector('.inpected-lay-wrap .sdk-menus-btn')?.click();
        setTimeout(() => {
          notification.success({
            className: 'inpected-lay-wrap',
            title: t('巡检完成'),
            description: <div>

              已完成对业务架构的巡检，您可以点击节点图标查看风险详情，或者
              <Button
                style={
                  {
                    position: 'relative',
                    top: '-1px',
                  }
                }
                disabled={generating}
                type="link"
                onClick={
                  () => {
                    createArchScanReportFile();
                  }
                }
              >
                <Slot content={
                  t('查看报告')
                }
                />
              </Button>
            </div>,
            duration: 0,
          });
        }, 200);
      }
      dispatch(changeReportData({
        isFinished: false,
        scanReportBubbleVisible: false,
      }));
    }
  }, [isFinished]);
  const getSvgStr = (str) => {
    if (!str) {
      return;
    }
    let newStr = str;
    if (newStr.indexOf('\'') === 0) {
      newStr = newStr.slice(1);
    }
    if (newStr.lastIndexOf('\'') === newStr.length - 1) {
      newStr = newStr.slice(0, newStr.length - 1);
    }
    // eslint-disable-next-line consistent-return
    return newStr;
  };
  const downloadFile = (url) => {
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    document.body.appendChild(iframe);
    iframe.src = url;
    setTimeout(() => {
      iframe.remove();
    }, 1000);
  };
  const getShowText = (str) => (str === undefined ? '-' : str);

  // 暴露给父组件的方法
  React.useImperativeHandle(ref, () => ({
    resetScanInfo,
  }));

   // 下载报告需要的pdf url
   const describeDownload = async (resultId) => {
      try {
        const res = await downloadTask({
          env: pluginAPI.env,
          uin: pluginAPI.uin,
          data: {
            ResultId: resultId,
          },
        });
        if (!res) {
          dispatch(changeReportData({
            generating: false,
          }));
          resetScanReportInfo();
          return;
        }
        dispatch(changeReportData({
          downloadInfo: res,
          scanReportBubbleVisible: false,
        }));
      } catch (err) {
        const message = err.msg || err.toString() || t('未知错误');
        app.tips.error(t('{{message}}', { message }));
      }
    };

   // 查询进度
  const describeDownloadTask = async (resultId) => {
      try {
        const res = await fetchArchScanReportTaskStatus({
          env: pluginAPI.env,
          uin: pluginAPI.uin,
          data: {
            ResultId: resultId,
          },
        });
        if (!res) {
          dispatch(changeReportData({
            generating: false,
          }));
          resetScanReportInfo();
          return;
        }
        dispatch(changeReportData({
          progressNum: res?.Progress ?? 0,
        }));
        if (res.TaskStatus === 'running' || res.TaskStatus === 'init') {
          const arr = res.CostTime.split(':');
          if ((window as any).initScanReportTime === null && !isLeave) {
            const time = (arr.length === 3 ? res.CostTime : `00:${res.CostTime}`);
            // initScanReportTime = time;
            (window as any).initScanReportTime = time;
            dispatch(changeReportData({
              scanReportTime: time,
            }));
          }
          if (arr?.length > 1 && parseInt(arr[arr.length - 2], 10) > 3) {
            dispatch(changeReportData({
              scanFail: true,
              generating: false,
              downloadInfo: res,
              scanReportBubbleVisible: true,
            }));
            resetScanReportInfo();
            return;
          }
          downloadTimer = setTimeout(() => {
            if (downloadTimer === null) {
              clearTimeout(downloadTimer);
              return;
            }
            visibleRef?.current && describeDownloadTask(resultId);
          }, 2000);
        } else if (res.TaskStatus === 'success') {
              // 重新获取cosurl
              getDescribeArchStrategyList()
          if (pluginAPI.env === 'ISA') {
            describeDownload(resultId);
          } else {
            dispatch(changeReportData({
              downloadInfo: res,
            }));
          }
          dispatch(changeReportData({
            generating: false,
            scanReportBubbleVisible: true,
            isFinished: false,
          }));
          resetScanReportInfo();
        }
      } catch (err) {
        resetScanReportInfo();
        dispatch(changeReportData({
          generating: false,
        }));
        const message = err.msg || err.toString() || t('未知错误');
        app.tips.error(t('{{message}}', { message }));
      }
  };

  useEffect(() => {
    return () => {
      clearInterval(scanReportTimeTimer);
    };
  }, []);
  return (
    <Modal
      visible={visible}
      className={`inspect-report-modal-real ${
        pluginPropsData.env === 'ISA' ? 'inspect-report-modal-real-isa' : ''
      }`}
      caption={
        <div className="head-btn-wrap">
          { (generating || scanFail) && reportBtnConfig?.content }
          { !generating && !scanFail && pluginPropsData.env === 'ISA' && (
            <Button
              className="download-btn"
              onClick={() => {
                downloadFile(downloadInfo.CosUrlPdf);
              }}
              type="link"
            >
              {t('下载报告')}
            </Button>
          )}
          { !generating && !scanFail && pluginPropsData.env === 'CONSOLE'
            && (archived ? (
              <ExternalLink
                href={
                  (pluginPropsData.env as Env) !== 'ISA'
                    ? `/advisor/digital-assets?${encodeURIComponent(
                      `archId=${currentMapId}&resultId=${resultId}`,
                    )}`
                    : `https://console.cloud.tencent.com/advisor/digital-assets?${encodeURIComponent(
                      `archId=${currentMapId}&resultId=${resultId}`,
                    )}`
                }
                onClick={() => {
                  clickReport(
                    `go-digital-assets;${new Date().getTime()};${resultId}`,
                  );
                }}
              >
                {t('点击前往"归档报告"')}
              </ExternalLink>
            ) : (
              <Button
                disabled={ generating || scanFail }
                type="link"
                onClick={() => {
                  clickReport(`file;${new Date().getTime()};${resultId}`);
                  updateArchScanReportArchiveInfo();
                }}
              >
                <>
                  <svg
                    width="14"
                    height="16"
                    viewBox="0 0 15 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M0.5 15.499V0.5H9.5L13.5 4.501V8H11.5V6H8V2.501H2.5V13.5H7V15.499H0.5ZM15 11.5V13.5H12.5V16H10.5V13.5H8V11.5H10.5V9H12.5V11.5H15Z"
                      fill="#006EFF"
                    />
                  </svg>
                  报告归档
                </>
              </Button>
            ))}
        </div>
      }
      onClose={() => {
        // onClose();
        resetScanReportInfo();
        dispatch(changeReportData({
          resultId: '',
          scanReportBubbleVisible: false,
          downloadInfo: {},
          generating: false,
          scanFail: false,
          visible: false,
          scanReportTime: null,
          progressNum: null
        }));
      }}
    >
      {pluginPropsData.env === 'CONSOLE' && (
        <Alert type="warning">
          <>
            当前为预览报告，如需对报告进行
            <span className="name">下载</span>
            、
            <span className="name">共享</span>
            、
            <span className="name">查看</span>
            等操作，请点击右上角“
            <span className="name">报告归档</span>
            ”，前往“
            <span className="name">数字资产</span>
            ”进行操作。
          </>
        </Alert>
      )}
      <Modal.Body>
        <div className="con-wrap">
          <div className="t-area">
            <div className="t-area-text">
              <div className="report-t">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 48 48"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M19.9885 36.8079L20 36.7997L13.583 23.1005C11.3833 18.2764 13.5107 12.5825 18.3348 10.3827C23.1588 8.18299 28.8527 10.3104 31.0525 15.1345L32.805 18.9778L48 19.9997L27.2 21.5997L34.91 39.801C36.8 44.1441 40.8 40.7997 33.4129 44.3319C25.8699 46.3283 32.8624 44.396 33.3544 44.2602L22.4 18.3997L29.1966 18.7773L28.1409 16.4621C26.6744 13.2461 22.8785 11.8278 19.6624 13.2943C16.4464 14.7608 15.0281 18.5568 16.4946 21.7728C20.7756 31.1611 22.4735 34.8259 23.2 36.7997C21.6 36.7997 21.6 36.7997 19.9885 36.8079Z"
                    fill="white"
                  />
                  <circle cx="19.1999" cy="17.6" r="1.6" fill="white" />
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M24 44.8C35.4875 44.8 44.8 35.4875 44.8 24C44.8 12.5125 35.4875 3.2 24 3.2C12.5125 3.2 3.2 12.5125 3.2 24C3.2 35.4875 12.5125 44.8 24 44.8ZM24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48Z"
                    fill="white"
                  />
                  <path
                    d="M4.80004 33.5999L20.8 33.5999L20.8 36.7999L4.80004 36.7999L4.80004 33.5999Z"
                    fill="white"
                  />
                </svg>
                <div>
                  {t('云顾问-云巡检架构评估报告-{{time}}', {
                    time: overviewInfo.ReportDate || '-',
                  })}
                </div>
              </div>
              <div className="report-t-en">
                Tencent Cloud Smart Advisor Architecture Evaluation Report
              </div>
              <div className="line">
                <div className="line-t">{t('名称：')}</div>
                <div className="line-desc">
                  {overviewInfo.CustomerName || '-'}
                </div>
              </div>
              <div className="line">
                <div className="line-t">{t('架构图名称：')}</div>
                <div className="line-desc">{overviewInfo.ArchName || '-'}</div>
              </div>
              <div className="line">
                <div className="line-t">{t('架构图ID：')}</div>
                <div className="line-desc">{overviewInfo.ArchId || '-'}</div>
              </div>
              <div className="line">
                <div className="line-t">APPID：</div>
                <div className="line-desc">{overviewInfo.AppId || '-'}</div>
              </div>
              <div className="line">
                <div className="line-t">{t('评估时间：')}</div>
                <div className="line-desc">
                  {overviewInfo.FinishTime || '-'}
                </div>
              </div>
            </div>
            <div className="t-area-bg">
              <svg
                className="svg-t"
                width="135"
                height="34"
                viewBox="0 0 151 34"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M58.3276 2.40188L55.4819 5.80908H26.1339L28.9796 2.40188H58.3276ZM33.0329 16.6603C25.8656 21.66 18.4757 26.2523 12.5464 29.7335H32.0384C32.0031 26.7708 32.0794 23.7709 32.2368 20.9193H37.6928C37.6182 24.8079 38.0673 29.2521 38.6651 33.8074H32.1091C32.0668 32.8445 32.0685 31.8816 32.0702 30.9187L29.1927 33.1407H0.900733C10.1976 27.7707 19.204 22.0303 26.6969 16.6603H13.8049L16.6066 13.2531H53.7426L50.9409 16.6603H33.0329ZM85.8409 4.43879H72.7729C64.9503 14.4382 55.8501 25.4005 47.7432 33.5111H42.9032C52.4295 24.6968 62.3234 12.7716 70.761 1.40194H88.317L85.8409 4.43879ZM105.817 4.43879H98.6009C97.6752 5.4017 96.4785 6.51274 95.4005 7.43861H99.6245C102.109 7.47564 102.277 8.10524 100.594 10.1792L88.1593 25.5857H83.9793L95.8073 10.9199C96.0889 10.5495 96.0079 10.4014 95.6119 10.4014H87.0759L74.8273 25.5857H70.6473L85.2805 7.43861H90.7365C91.9025 6.51274 93.0755 5.43873 94.0689 4.43879H86.8969L89.285 1.40194H108.205L105.817 4.43879ZM66.471 29.2151L62.7559 32.4H51.7119L70.9481 8.58669H79.6601C81.8161 8.58669 81.6691 9.36442 80.4513 10.9199L69.8754 23.7709H64.4634L65.4031 20.66H68.3071L75.1688 12.3642C75.5894 11.8457 75.5999 11.6235 75.1159 11.6235H72.6079L58.463 29.2151H66.471ZM79.7984 26.0671H83.2744C83.074 28.4373 83.3804 31.252 83.8531 33.8074H78.4851C78.411 31.6594 78.5033 29.2521 78.7478 26.8819C75.2332 29.5484 70.7982 32.1408 67.2211 33.8074H60.9291C66.2195 31.7334 71.8874 28.6595 75.7645 25.7708C79.5739 22.9191 84.3633 17.0306 88.2341 11.9569H92.5021C88.1465 17.5121 83.5743 22.9932 79.7984 26.0671ZM122.018 7.03123H116.87C117.053 5.03135 117.436 2.51298 117.819 0.476074H122.615C122.355 2.25374 122.127 4.73507 122.018 7.03123ZM148.624 1.29084C150.736 1.29084 150.749 1.95746 149.578 3.43886L125.213 33.8074H114.565L115.559 30.4742H122.995L143.124 5.47577C143.734 4.661 143.43 4.58693 142.55 4.58693H126.49L126.844 1.29084H148.624ZM88.6051 33.8074L111.755 5.4017L114.567 7.80896L93.4891 33.8074H88.6051ZM132.71 12.5864L122.117 25.8079H104.737L117.45 10.1051H132.058C134.214 10.1051 134.091 10.8458 132.71 12.5864ZM126.938 13.6975C127.243 13.2901 127.138 13.179 126.698 13.179H119.79L112.121 22.734H119.733L126.938 13.6975Z"
                  fill="white"
                />
              </svg>
              <div>
                <svg
                  className="sub-svg-t"
                  width="104"
                  height="17"
                  viewBox="0 0 136 17"
                  fill="none"
                >
                  <rect
                    width="136"
                    height="17"
                    fill="url(#pattern0_8082_10002)"
                  />
                  <defs>
                    <pattern
                      id="pattern0_8082_10002"
                      patternContentUnits="objectBoundingBox"
                      width="1"
                      height="1"
                    >
                      <use
                        xlinkHref="#image0_8082_10002"
                        transform="matrix(0.00298507 0 0 0.0238806 0 -0.0253731)"
                      />
                    </pattern>
                    <image
                      id="image0_8082_10002"
                      width="335"
                      height="44"
                      xlinkHref="data:image/png;base64,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"
                    />
                  </defs>
                </svg>
              </div>
            </div>
          </div>
          <Card>
            <Card.Body>
              <div className="card-t">{t('架构图云巡检概览')}</div>
              {/* <div className="overview-t-desc"> */}
              {/*   本次巡检已扫描 */}
              {/*   <Slot content={ */}
              {/*     getShowText(overviewInfo.ArchScanSourceInfo?.ScanStrategyCount) */}
              {/*   } /> */}
              {/*   项风险项，覆盖架构图资源 */}
              {/*   <Slot content={ */}
              {/*     getShowText(overviewInfo.ArchScanSourceInfo?.ScanResourceCount) */}
              {/*   } /> */}
              {/*   个，占账户整体资源数的&nbsp; */}
              {/*   <span className={'percent-num'}> */}
              {/*     <Slot content={ */}
              {/*       getShowText(overviewInfo.ArchScanSourceInfo?.ScanResourcePercent) */}
              {/*     } />% */}
              {/*   </span>。 */}
              {/* </div> */}
              {/* <div className="overview-t-desc-sub"> */}
              {/*   { */}
              {/*     t('*本架构图中，仍有{{unScanResourceCount}}个资源未被巡检策略覆盖、另有{{unBindResourceCount}}个资源未绑定在本架构图，其巡检结果暂未包含在报告中。', { */}
              {/*       unScanResourceCount: getShowText(overviewInfo.ArchScanSourceInfo?.UnScanResourceCount), */}
              {/*       unBindResourceCount: getShowText(overviewInfo.ArchScanSourceInfo?.UnBindResourceCount), */}
              {/*     }) */}
              {/*   } */}
              {/* </div> */}
              <div className="overview-t-desc">
                {t(
                  '本次巡检已扫描{{inspectCount}}项风险项，包含{{productCount}}款云产品，涉及实例资源{{resourceCount}}个。',
                  {
                    inspectCount: getShowText(
                      overviewInfo.ArchScanSourceInfo?.ScanStrategyCount,
                    ),
                    productCount: getShowText(
                      overviewInfo.ArchScanSourceInfo?.ScanProductCount,
                    ),
                    resourceCount: getShowText(
                      overviewInfo.ArchScanSourceInfo?.ScanResourceCount,
                    ),
                  },
                )}
              </div>
              <div
                style={{
                  height: 'auto',
                }}
                className="chart-outer"
              >
                <div
                  className="pic-wrap"
                  // eslint-disable-next-line react/no-danger
                  dangerouslySetInnerHTML={{
                    __html: getSvgStr(riskTrendInfo?.Svg),
                  }}
                />
              </div>
              <div className="risk-items-wrap">
                <div className="risk-items">
                  <div className="items-t">{t('已扫风险项')}</div>
                  <div className="item-num">
                    {overviewInfo.CurrentStrategySummaryInfo?.ScanCount || '-'}
                  </div>
                  <div className="item-desc">
                    {overviewInfo.LastWeekStrategySummaryInfo?.ScanCount
                      === '--'
                    || !overviewInfo.LastWeekStrategySummaryInfo?.ScanCount ? (
                        t('同比上周 -')
                      ) : (
                        <>
                          同比上周
                          <Slot
                            content={
                            overviewInfo.LastWeekStrategySummaryInfo
                              ?.ScanCount > 0 ? (
                                <svg
                                  width="8"
                                  height="12"
                                  viewBox="0 0 8 12"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M7.5 5.69922L4.00033 0.699219L0.5 5.69922L3 5.69922L3 11.1992H5L5 5.69922L7.5 5.69922Z"
                                    fill="#D54941"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  width="8"
                                  height="11"
                                  viewBox="0 0 8 11"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M0.500001 5.69922L3.99967 10.6992L7.5 5.69922L5 5.69922L5 0.199219L3 0.199218L3 5.69922L0.500001 5.69922Z" />
                                </svg>
                              )
                          }
                          />
                          <Slot
                            content={Math.abs(
                              overviewInfo.LastWeekStrategySummaryInfo?.ScanCount,
                            )}
                          />
                        </>
                      )}
                  </div>
                </div>
                <div className="risk-items">
                  <div className="items-t">{t('高风险项')}</div>
                  <div className="item-num">
                    {overviewInfo.CurrentStrategySummaryInfo?.HighRiskCount
                      || '-'}
                  </div>
                  <div className="item-desc">
                    {overviewInfo.LastWeekStrategySummaryInfo?.HighRiskCount
                      === '--'
                    || !overviewInfo.LastWeekStrategySummaryInfo?.HighRiskCount ? (
                        t('同比上周 -')
                      ) : (
                        <>
                          同比上周
                          <Slot
                            content={
                            overviewInfo.LastWeekStrategySummaryInfo
                              ?.HighRiskCount > 0 ? (
                                <svg
                                  width="8"
                                  height="12"
                                  viewBox="0 0 8 12"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M7.5 5.69922L4.00033 0.699219L0.5 5.69922L3 5.69922L3 11.1992H5L5 5.69922L7.5 5.69922Z"
                                    fill="#D54941"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  width="8"
                                  height="11"
                                  viewBox="0 0 8 11"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M0.500001 5.69922L3.99967 10.6992L7.5 5.69922L5 5.69922L5 0.199219L3 0.199218L3 5.69922L0.500001 5.69922Z" />
                                </svg>
                              )
                          }
                          />
                          <Slot
                            content={Math.abs(
                              overviewInfo.LastWeekStrategySummaryInfo
                                ?.HighRiskCount,
                            )}
                          />
                        </>
                      )}
                  </div>
                </div>
                <div className="risk-items">
                  <div className="items-t">{t('中风险项')}</div>
                  <div className="item-num">
                    {overviewInfo.CurrentStrategySummaryInfo?.MediumRiskCount
                      || '-'}
                  </div>
                  <div className="item-desc">
                    {overviewInfo.LastWeekStrategySummaryInfo
                      ?.MediumRiskCount === '--'
                    || !overviewInfo.LastWeekStrategySummaryInfo
                      ?.MediumRiskCount ? (
                        t('同比上周 -')
                      ) : (
                        <>
                          同比上周
                          <Slot
                            content={
                            overviewInfo.LastWeekStrategySummaryInfo
                              ?.MediumRiskCount > 0 ? (
                                <svg
                                  width="8"
                                  height="12"
                                  viewBox="0 0 8 12"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M7.5 5.69922L4.00033 0.699219L0.5 5.69922L3 5.69922L3 11.1992H5L5 5.69922L7.5 5.69922Z"
                                    fill="#D54941"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  width="8"
                                  height="11"
                                  viewBox="0 0 8 11"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M0.500001 5.69922L3.99967 10.6992L7.5 5.69922L5 5.69922L5 0.199219L3 0.199218L3 5.69922L0.500001 5.69922Z" />
                                </svg>
                              )
                          }
                          />
                          <Slot
                            content={Math.abs(
                              overviewInfo.LastWeekStrategySummaryInfo
                                ?.MediumRiskCount,
                            )}
                          />
                        </>
                      )}
                  </div>
                </div>
                <div className="risk-items">
                  <div className="items-t">{t('健康项')}</div>
                  <div className="item-num">
                    {overviewInfo.CurrentStrategySummaryInfo?.NoRiskCount
                      || '-'}
                  </div>
                  <div className="item-desc">
                    {overviewInfo.LastWeekStrategySummaryInfo?.NoRiskCount
                      === '--'
                    || !overviewInfo.LastWeekStrategySummaryInfo?.NoRiskCount ? (
                        t('同比上周 -')
                      ) : (
                        <>
                          同比上周
                          <Slot
                            content={
                            overviewInfo.LastWeekStrategySummaryInfo
                              ?.NoRiskCount > 0 ? (
                                <svg
                                  width="8"
                                  height="12"
                                  viewBox="0 0 8 12"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path
                                    d="M7.5 5.69922L4.00033 0.699219L0.5 5.69922L3 5.69922L3 11.1992H5L5 5.69922L7.5 5.69922Z"
                                    fill="#D54941"
                                  />
                                </svg>
                              ) : (
                                <svg
                                  width="8"
                                  height="11"
                                  viewBox="0 0 8 11"
                                  fill="none"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <path d="M0.500001 5.69922L3.99967 10.6992L7.5 5.69922L5 5.69922L5 0.199219L3 0.199218L3 5.69922L0.500001 5.69922Z" />
                                </svg>
                              )
                          }
                          />
                          <Slot
                            content={Math.abs(
                              overviewInfo.LastWeekStrategySummaryInfo
                                ?.NoRiskCount,
                            )}
                          />
                        </>
                      )}
                  </div>
                </div>
              </div>
              <div className="chart-wrap">
                <div className="chart-item chart-item-br">
                  <div
                    style={{
                      position: 'relative',
                    }}
                    className="title"
                  >
                    {overviewInfo.ArchScanScoreTrendInfo?.Name || '-'}
                    <div
                      style={{
                        position: 'absolute',
                        right: '24px',
                        top: '-4px',
                        fontSize: '48px',
                        lineHeight: '48px',
                        color:
                          // eslint-disable-next-line no-nested-ternary
                          overviewInfo?.CurrentScanScore >= 85
                          && overviewInfo?.CurrentScanScore <= 100
                            ? '#0CBF5B'
                            : overviewInfo?.CurrentScanScore >= 61
                              && overviewInfo?.CurrentScanScore <= 84
                              ? '#FF7800'
                              : '#F64041',
                      }}
                    >
                      {overviewInfo?.CurrentScanScore || '-'}
                    </div>
                  </div>
                  <ExternalLink
                    href="https://cloud.tencent.com/document/product/1264/106740#46728c07-1735-4f2d-aa24-ca1a675633ef"
                    onClick={() => {
                      clickReport(
                        `see-score-description;${new Date().getTime()};${resultId}`,
                      );
                    }}
                    style={{
                      marginRight: '10px',
                      position: 'relative',
                      zIndex: 2,
                    }}
                  >
                    {t('巡检得分说明')}
                  </ExternalLink>
                  <div
                    style={{
                      marginTop: '-14px',
                    }}
                    className="chart-outer"
                  >
                    <TvisionTcharts
                      chartType="line"
                      options={
                        {
                          animation: false,
                          title: {
                            text: overviewInfo.ArchScanScoreTrendInfo
                              ?.ChartInfoSet?.[0]?.TitleName,
                            show: true,
                            textStyle: {
                              fontSize: 12,
                            },
                          },
                          xAxis: {
                            boundaryGap: false,
                            data: overviewInfo.ArchScanScoreTrendInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet?.map(
                              (item) => item.Key,
                            ),
                          },
                          yAxis: {
                            interval:
                              max([
                                ...(overviewInfo.ArchScanScoreTrendInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                                  (item) => item.Value,
                                ) || []),
                                ...(overviewInfo.ArchScanScoreTrendInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[1]?.KeyValueSet.map(
                                  (item) => item.Value,
                                ) || []),
                              ]) >= 5
                                ? undefined
                                : 1,
                            type: 'value',
                          },
                          series: [
                            {
                              name: overviewInfo.ArchScanScoreTrendInfo
                                ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]
                                ?.DataName,
                              type: 'line',
                              color: '#2281FE',
                              data: overviewInfo.ArchScanScoreTrendInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                                (item) => item.Value,
                              ),
                              lineStyle: {
                                width: 2,
                              },
                            },
                            {
                              name: overviewInfo.ArchScanScoreTrendInfo
                                ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[1]
                                ?.DataName,
                              type: 'line',
                              color: '#0CBF5B',
                              data: overviewInfo.ArchScanScoreTrendInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[1]?.KeyValueSet.map(
                                (item) => item.Value,
                              ),
                              lineStyle: {
                                width: 2,
                              },
                            },
                          ],
                        } as any
                      }
                      theme="tvision"
                    />
                  </div>
                </div>
                <div className="chart-item chart-item-br">
                  <div className="title">
                    {overviewInfo.ArchScanGroupScoreInfo?.Name || '-'}
                  </div>
                  <div
                    style={{
                      marginTop: '-60px',
                    }}
                    className="chart-outer"
                  >
                    <TvisionTcharts
                      chartType="bar"
                      options={
                        {
                          animation: false,
                          title: {
                            text: '',
                            show: false,
                          },
                          tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                              type: 'shadow',
                            },
                          },
                          legend: {},
                          grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true,
                          },
                          xAxis: {
                            type: 'value',
                            boundaryGap: [0, 0.01],
                          },
                          yAxis: {
                            type: 'category',
                            data: overviewInfo.ArchScanGroupScoreInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                              (item) => item.Key,
                            ),
                          },
                          series: [
                            {
                              // name: '2011',
                              type: 'bar',
                              barWidth: 20,
                              label: {
                                show: true,
                                color: '#fff',
                                formatter(param) {
                                  return param === 0 ? '' : param;
                                },
                              },
                              color:
                                overviewInfo.ArchScanGroupScoreInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                                  // eslint-disable-next-line no-nested-ternary
                                  (item) => (item.Value >= 85 && item.Value <= 100
                                    ? '#0CBF5B'
                                    : item.Value >= 61 && item.Value <= 84
                                      ? '#FF7800'
                                      : '#F64041'),
                                ),
                              data: overviewInfo.ArchScanGroupScoreInfo?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                                (item) => item.Value,
                              ),
                            },
                          ],
                        } as any
                      }
                      theme="tvision"
                    />
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
          <Card>
            <Card.Body>
              <div className="card-t">{t('架构图云上治理趋势')}</div>
              <div
                style={{
                  marginTop: '24px',
                }}
                className="chart-wrap chart-wrap-br"
              >
                <div
                  style={{
                    width: '100%',
                  }}
                >
                  <div
                    className="title"
                    style={{
                      paddingTop: '24px',
                    }}
                  >
                    {riskTrendInfo.ArchRiskChartInfos?.[0]?.Name || '-'}
                  </div>
                  <div
                    style={{
                      marginTop: '-20px',
                    }}
                    className="chart-outer"
                  >
                    <TvisionTcharts
                      chartType="line"
                      options={
                        {
                          title: {
                            text: t('风险项（单位：项）'),
                            show: true,
                            textStyle: {
                              fontSize: 12,
                              fontWeight: 'normal',
                              color: 'rgba(0, 0, 0, 0.60)',
                            },
                          },
                          xAxis: {
                            boundaryGap: false,
                            data: riskTrendInfo.ArchRiskChartInfos?.[0]?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                              (item) => item.Key,
                            ),
                          },
                          yAxis: {
                            interval:
                              max([
                                ...(riskTrendInfo.ArchRiskChartInfos?.[0]?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                                  (item) => item.Value,
                                ) || []),
                                ...(riskTrendInfo.ArchRiskChartInfos?.[0]?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[1]?.KeyValueSet.map(
                                  (item) => item.Value,
                                ) || []),
                              ]) >= 5
                                ? undefined
                                : 1,
                            type: 'value',
                          },
                          series: [
                            {
                              name: riskTrendInfo.ArchRiskChartInfos?.[0]
                                ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]
                                ?.DataName,
                              type: 'line',
                              color: '#F64041',
                              data: riskTrendInfo.ArchRiskChartInfos?.[0]?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]?.KeyValueSet.map(
                                (item) => item.Value,
                              ),
                              lineStyle: {
                                width: 2,
                              },
                            },
                            {
                              name: riskTrendInfo.ArchRiskChartInfos?.[0]
                                ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[1]
                                ?.DataName,
                              type: 'line',
                              color: '#FF7800',
                              data: riskTrendInfo.ArchRiskChartInfos?.[0]?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[1]?.KeyValueSet.map(
                                (item) => item.Value,
                              ),
                              lineStyle: {
                                width: 2,
                              },
                            },
                          ],
                        } as any
                      }
                      theme="tvision"
                    />
                  </div>
                </div>
              </div>
              <div
                style={{
                  marginTop: '24px',
                }}
                className="chart-wrap chart-wrap-br"
              >
                <div
                  style={{
                    width: '100%',
                  }}
                >
                  <div
                    style={{
                      paddingTop: '24px',
                    }}
                    className="title"
                  >
                    {riskTrendInfo.ArchRiskChartInfos?.[1]?.Name || '-'}
                  </div>
                  <div
                    style={{
                      // eslint-disable-next-line no-unsafe-optional-chaining
                      height: `${
                        300
                        // eslint-disable-next-line no-unsafe-optional-chaining
                        + (riskTrendInfo.ArchRiskChartInfos?.[1]
                          ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.length
                          - 3)
                          * 50
                      }px`,
                      marginTop:
                        riskTrendInfo.ArchRiskChartInfos?.[1]?.ChartInfoSet?.[0]
                          ?.ChartDataInfoSet?.length > 3
                          ? '-60px'
                          : '-64px',
                    }}
                    className="chart-outer"
                  >
                    {riskProList?.length > 0 && (
                      <TvisionTcharts
                        chartType="bar"
                        options={
                          {
                            title: {
                              text: '',
                              show: false,
                            },
                            tooltip: {
                              trigger: 'axis',
                              axisPointer: {
                                type: 'shadow',
                              },
                            },
                            legend: {},
                            grid: {
                              left: '3%',
                              right: '4%',
                              bottom: '3%',
                              containLabel: true,
                            },
                            xAxis: {
                              type: 'value',
                              boundaryGap: [0, 0.01],
                            },
                            yAxis: {
                              type: 'category',
                              data: riskTrendInfo.ArchRiskChartInfos?.[1]?.ChartInfoSet?.[0]?.ChartDataInfoSet?.map(
                                (item) => item.DataName,
                              ),
                            },
                            series: [
                              {
                                name: riskTrendInfo.ArchRiskChartInfos?.[1]
                                  ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]
                                  ?.KeyValueSet?.[0]?.Key,
                                type: 'bar',
                                stack: 'total',
                                barWidth: 20,
                                label: {
                                  show: true,
                                  color: '#fff',
                                  formatter(param) {
                                    return param === 0 ? '' : param;
                                  },
                                },
                                emphasis: {
                                  focus: 'series',
                                },
                                color: '#F64041',
                                data: riskProList?.[0] || [],
                              },
                              {
                                name: riskTrendInfo.ArchRiskChartInfos?.[1]
                                  ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]
                                  ?.KeyValueSet?.[1]?.Key,
                                type: 'bar',
                                stack: 'total',
                                barWidth: 20,
                                label: {
                                  show: true,
                                  color: '#fff',
                                  formatter(param) {
                                    return param === 0 ? '' : param;
                                  },
                                },
                                emphasis: {
                                  focus: 'series',
                                },
                                color: '#FF7800',
                                data: riskProList?.[1] || [],
                              },
                              {
                                name: riskTrendInfo.ArchRiskChartInfos?.[1]
                                  ?.ChartInfoSet?.[0]?.ChartDataInfoSet?.[0]
                                  ?.KeyValueSet?.[2]?.Key,
                                type: 'bar',
                                stack: 'total',
                                barWidth: 20,
                                label: {
                                  show: true,
                                  color: '#fff',
                                  formatter(param) {
                                    return param === 0 ? '' : param;
                                  },
                                },
                                emphasis: {
                                  focus: 'series',
                                },
                                color: '#0CBF5B',
                                data: riskProList?.[2] || [],
                              },
                            ],
                          } as any
                        }
                        theme="tvision"
                      />
                    )}
                  </div>
                </div>
              </div>
              <div
                style={{
                  marginTop: '24px',
                }}
                className="chart-wrap chart-wrap-br"
              >
                <div
                  style={{
                    width: '100%',
                  }}
                >
                  <div
                    style={{
                      padding: '24px 0 0 24px',
                    }}
                    className="title"
                  >
                    {riskTrendInfo.ArchRiskChartInfos?.[2]?.Name || '-'}
                  </div>
                  <div className="chart-list">
                    <div className="desc-wrap line-pic-item">
                      <div className="desc-t-wrap">
                        <div className="desc-t">
                          {t('云顾问卓越架构五大维度：')}
                        </div>
                        <div className="desc-t">
                          {t('安全、可靠、性能、成本和服务限制')}
                        </div>
                      </div>
                      <div className="desc-line">
                        <div>{t('一、安全提升系统业务安全性；')}</div>
                        <div>{t('二、可靠维护实例稳定；')}</div>
                        <div>{t('三、性能提供优化建议；')}</div>
                        <div>{t('四、成本推荐高性价比配置方案；')}</div>
                        <div>{t('五、服务限制关注资源数量及配额。')}</div>
                      </div>
                    </div>
                    {riskTrendInfo.ArchRiskChartInfos?.[2]?.ChartInfoSet?.map(
                      (item, j) => (
                        <div key={j} className="line-pic-item">
                          <div
                            style={{
                              textAlign: 'right',
                              fontSize: '12px',
                              color: 'rgba(0,0,0,0.6)',
                              marginBottom: '-55px',
                            }}
                          >
                            {t('风险项（单位：项）')}
                          </div>
                          <TvisionTcharts
                            chartType="line"
                            options={
                              {
                                title: {
                                  text: item.TitleName,
                                  show: true,
                                  textStyle: {
                                    fontSize: 12,
                                  },
                                },
                                xAxis: {
                                  boundaryGap: false,
                                  data: item?.ChartDataInfoSet?.[0]?.KeyValueSet?.map(
                                    (item) => item.Key,
                                  ),
                                },
                                yAxis: {
                                  interval:
                                    max([
                                      ...(item?.ChartDataInfoSet?.map((el) => el?.KeyValueSet?.map((val) => val.Value)) || []),
                                    ]) >= 5
                                      ? undefined
                                      : 1,
                                  type: 'value',
                                },
                                series: item?.ChartDataInfoSet?.map(
                                  (el, i) => ({
                                    name: el?.DataName,
                                    type: 'line',
                                    color: i === 0 ? '#F64041' : '#FF7800',
                                    data: el?.KeyValueSet?.map(
                                      (val) => val.Value,
                                    ),
                                    lineStyle: {
                                      width: 2,
                                    },
                                  }),
                                ),
                              } as any
                            }
                            theme="tvision"
                          />
                        </div>
                      ),
                    )}
                  </div>
                </div>
              </div>
            </Card.Body>
          </Card>
          <Card>
            <Card.Body>
              <div className="card-t">
                <>架构图云巡检明细清单</>
              </div>
              <Table
                className="inspect-see-report-tab"
                bordered
                records={records || []}
                columns={columns || []}
                addons={[
                  scrollable({
                    maxHeight: 500,
                  }),
                ]}
                topTip={records?.length === 0 && <StatusTip status="empty" />}
              />
            </Card.Body>
          </Card>
          {!generating && !scanFail && <div className="download-wrap">
            <div>{t('更多优化建议，请点击 ')}</div>
            <Button
              onClick={() => {
                clickReport(
                  `download-excel;${new Date().getTime()};${resultId}`,
                );
                downloadFile(url);
              }}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 18 24"
                fill="#000"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M1.16667 21.6667H16.8333C17.4777 21.6667 18 22.1891 18 22.8334C18 23.4455 17.5286 23.9475 16.929 23.9962L16.8333 24.0001H1.16667C0.522335 24.0001 0 23.4777 0 22.8334C0 22.2213 0.471407 21.7193 1.07098 21.6706L1.16667 21.6667ZM8.73765 0.170616L8.83333 0.166748C9.44545 0.166748 9.94746 0.638155 9.99613 1.23773L10 1.33341V15.5554L13.1954 12.3606C13.651 11.905 14.3897 11.905 14.8453 12.3606C15.3009 12.8162 15.3009 13.5549 14.8453 14.0105L9.65988 19.1959C9.20427 19.6516 8.46558 19.6516 8.00997 19.1959L2.82452 14.0105C2.36891 13.5549 2.36891 12.8162 2.82452 12.3606C3.28013 11.905 4.01882 11.905 4.47443 12.3606L7.66667 15.5527V1.33341C7.66667 0.721299 8.13807 0.219286 8.73765 0.170616Z" />
              </svg>
              {t('下载详细数据')}
            </Button>
          </div>}
        </div>
      </Modal.Body>
    </Modal>
  );
});
Report.displayName = 'Report';
export default Report;
