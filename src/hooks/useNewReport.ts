import { useRef, useEffect } from 'react';
import { debounce } from 'lodash';
import { useDispatch } from 'react-redux';
import { app } from '@tea/app';
import { createArchScanReportFile as createArchScanReport } from '@src/service/export-service/final-request';
import { t } from '@tea/app/i18n';
import { reportVersion } from '@src/constant';
import { changeReportData, useReportSelector } from '@src/store/report/index';

/**
 * 生成报告hook
 * @returns
 */

interface IuseInitProps {
  pluginAPI: AppPluginAPI.PluginAPI;
}

export default function useNewReport(props: IuseInitProps) {
  const {
    pluginAPI,
  } = props;
  const dispatch = useDispatch();
  const { visible } = useReportSelector();
  const visibleRef = useRef(null);

  // 生成报告
  const openreportModal = debounce(async () => {
    if (visibleRef.current) {
      return;
    }
    if (pluginAPI.env === 'ISA') {
      (document as any).querySelector('.inpected-lay-wrap  .tea-notification__close .tea-btn')?.click();
    } else {
      (document as any).querySelector('.inpected-lay-wrap .sdk-menus-btn')?.click();
    }
    try {
      const res = await createArchScanReport({
        env: pluginAPI.env,
        uin: pluginAPI.uin,
        data: {
          CloudMapUuid: pluginAPI.archInfo.archId,
          TaskType: 'mapTaskType',
          ReportVersion: reportVersion,
          SdkName: 'inspect-report-sdk',
        },
      });
      if (!res) {
        return;
      }
      dispatch(changeReportData({
        resultId: res.ResultId,
        scanReportBubbleVisible: true,
        downloadInfo: {},
        generating: true,
        scanFail: false,
        visible: true,
      }));
    } catch (err) {
      dispatch(changeReportData({
        generating: false,
      }));
      const message = err.msg || err.toString() || t('未知错误');
      app.tips.error(t('{{message}}', { message }));
    }
  }, 300);

  useEffect(() => {
    visibleRef.current = visible;
  }, [visible]);

  return { openreportModal } as any;
}
